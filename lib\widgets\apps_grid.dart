import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../constants/app_strings.dart';
import '../models/app_item.dart';

/// Grilla de aplicaciones optimizada para personas mayores
/// Muestra aplicaciones con iconos grandes y texto claro
class AppsGrid extends StatefulWidget {
  final Function(String) onAppTap;

  const AppsGrid({
    super.key,
    required this.onAppTap,
  });

  @override
  State<AppsGrid> createState() => _AppsGridState();
}

class _AppsGridState extends State<AppsGrid> {
  List<AppItem> _favoriteApps = [];

  @override
  void initState() {
    super.initState();
    _loadFavoriteApps();
  }

  void _loadFavoriteApps() {
    // Aplicaciones predefinidas comunes para personas mayores
    _favoriteApps = [
      AppItem(
        name: AppStrings.phone,
        packageName: 'com.android.dialer',
        icon: Icons.phone,
        color: AppColors.accent,
      ),
      AppItem(
        name: AppStrings.messages,
        packageName: 'com.android.mms',
        icon: Icons.message,
        color: AppColors.primary,
      ),
      AppItem(
        name: AppStrings.camera,
        packageName: 'com.android.camera2',
        icon: Icons.camera_alt,
        color: AppColors.info,
      ),
      AppItem(
        name: AppStrings.gallery,
        packageName: 'com.android.gallery3d',
        icon: Icons.photo_library,
        color: AppColors.warning,
      ),
      AppItem(
        name: AppStrings.internet,
        packageName: 'com.android.chrome',
        icon: Icons.language,
        color: AppColors.primary,
      ),
      AppItem(
        name: AppStrings.email,
        packageName: 'com.android.email',
        icon: Icons.email,
        color: AppColors.accent,
      ),
      AppItem(
        name: AppStrings.calendar,
        packageName: 'com.android.calendar',
        icon: Icons.calendar_today,
        color: AppColors.error,
      ),
      AppItem(
        name: AppStrings.contacts,
        packageName: 'com.android.contacts',
        icon: Icons.contacts,
        color: AppColors.success,
      ),
      AppItem(
        name: AppStrings.music,
        packageName: 'com.android.music',
        icon: Icons.music_note,
        color: AppColors.adminPrimary,
      ),
      AppItem(
        name: AppStrings.weather,
        packageName: 'com.android.weather',
        icon: Icons.wb_sunny,
        color: AppColors.warning,
      ),
      AppItem(
        name: AppStrings.settings,
        packageName: 'com.android.settings',
        icon: Icons.settings,
        color: AppColors.textSecondary,
      ),
      AppItem(
        name: AppStrings.help,
        packageName: 'help',
        icon: Icons.help,
        color: AppColors.info,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Determinar número de columnas según el tamaño de pantalla
    int crossAxisCount;
    double childAspectRatio;
    double spacing;
    EdgeInsets padding;

    if (screenWidth > 1200) {
      // Tablets muy grandes o pantallas de escritorio
      crossAxisCount = 6;
      childAspectRatio = 0.9;
      spacing = AppDimensions.paddingL;
      padding = const EdgeInsets.all(AppDimensions.paddingXL);
    } else if (screenWidth > 900) {
      // Tablets grandes
      crossAxisCount = 5;
      childAspectRatio = 0.95;
      spacing = AppDimensions.paddingM;
      padding = const EdgeInsets.all(AppDimensions.paddingL);
    } else if (screenWidth > 600) {
      // Tablets medianas
      crossAxisCount = 4;
      childAspectRatio = 1.0;
      spacing = AppDimensions.paddingM;
      padding = const EdgeInsets.all(AppDimensions.paddingM);
    } else {
      // Teléfonos o tablets pequeñas
      crossAxisCount = 3;
      childAspectRatio = 1.1;
      spacing = AppDimensions.paddingS;
      padding = const EdgeInsets.all(AppDimensions.paddingS);
    }

    return GridView.builder(
      padding: padding,
      physics: const BouncingScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: _favoriteApps.length,
      itemBuilder: (context, index) {
        final app = _favoriteApps[index];
        final isTablet = screenWidth > 600;
        return _buildAppCard(app, isTablet);
      },
    );
  }

  Widget _buildAppCard(AppItem app, [bool isTablet = false]) {
    return GestureDetector(
      onTap: () => widget.onAppTap(app.packageName),
      child: Container(
        decoration: BoxDecoration(
          gradient: AppColors.surfaceGradient,
          borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.25),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: app.color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: app.color.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
            onTap: () => widget.onAppTap(app.packageName),
            splashColor: app.color.withValues(alpha: 0.2),
            highlightColor: app.color.withValues(alpha: 0.1),
            child: Padding(
              padding: EdgeInsets.all(
                isTablet ? AppDimensions.paddingL : AppDimensions.paddingM,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icono de la aplicación con gradiente
                  Expanded(
                    flex: 4,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            app.color.withValues(alpha: 0.8),
                            app.color,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(
                          isTablet ? AppDimensions.radiusXL : AppDimensions.radiusL,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: app.color.withValues(alpha: 0.3),
                            blurRadius: isTablet ? 8 : 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        app.icon,
                        size: isTablet ? 48 : AppDimensions.iconXL,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  SizedBox(height: isTablet ? AppDimensions.paddingS : AppDimensions.paddingXS),

                  // Nombre de la aplicación
                  Text(
                    app.name,
                    style: TextStyle(
                      fontSize: isTablet ? AppDimensions.fontM : AppDimensions.fontS,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                      shadows: const [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
