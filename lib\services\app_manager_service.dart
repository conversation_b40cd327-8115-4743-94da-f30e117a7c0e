import 'package:flutter/material.dart';
import 'package:installed_apps/installed_apps.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_item.dart';
import '../constants/app_colors.dart';

/// Servicio para gestionar aplicaciones instaladas y favoritas
class AppManagerService {
  static AppManagerService? _instance;
  static AppManagerService get instance => _instance ??= AppManagerService._();
  
  AppManagerService._();

  // Cache de aplicaciones
  List<AppItem> _installedApps = [];
  List<AppItem> _favoriteApps = [];
  
  /// Apps populares predefinidas con package names comunes
  static const Map<String, AppData> _popularApps = {
    // Comunicación
    'com.whatsapp': AppData('WhatsApp', Icons.chat, AppColors.success),
    'com.facebook.orca': AppData('Messenger', Icons.message, AppColors.primary),
    'com.skype.raider': AppData('Skype', Icons.video_call, AppColors.info),
    'com.viber.voip': AppData('Viber', Icons.phone, AppColors.accent),
    'org.telegram.messenger': AppData('Telegram', Icons.send, AppColors.primary),
    
    // Redes Sociales
    'com.facebook.katana': AppData('Facebook', Icons.facebook, AppColors.primary),
    'com.instagram.android': AppData('Instagram', Icons.camera_alt, AppColors.accent),
    'com.twitter.android': AppData('Twitter', Icons.alternate_email, AppColors.info),
    'com.linkedin.android': AppData('LinkedIn', Icons.work, AppColors.primary),
    
    // Entretenimiento
    'com.google.android.youtube': AppData('YouTube', Icons.play_circle_fill, AppColors.error),
    'com.netflix.mediaclient': AppData('Netflix', Icons.movie, AppColors.error),
    'com.spotify.music': AppData('Spotify', Icons.music_note, AppColors.success),
    'com.amazon.avod.thirdpartyclient': AppData('Prime Video', Icons.tv, AppColors.info),
    'com.disney.disneyplus': AppData('Disney+', Icons.star, AppColors.primary),
    
    // Productividad
    'com.microsoft.office.outlook': AppData('Outlook', Icons.email, AppColors.primary),
    'com.google.android.gm': AppData('Gmail', Icons.mail, AppColors.error),
    'com.microsoft.office.word': AppData('Word', Icons.description, AppColors.primary),
    'com.adobe.reader': AppData('PDF Reader', Icons.picture_as_pdf, AppColors.error),
    'com.dropbox.android': AppData('Dropbox', Icons.cloud, AppColors.primary),
    
    // Navegación y Mapas
    'com.google.android.apps.maps': AppData('Maps', Icons.map, AppColors.success),
    'com.waze': AppData('Waze', Icons.navigation, AppColors.info),
    'com.uber.app': AppData('Uber', Icons.local_taxi, AppColors.textPrimary),
    
    // Compras y Finanzas
    'com.amazon.mShop.android.shopping': AppData('Amazon', Icons.shopping_cart, AppColors.warning),
    'com.paypal.android.p2pmobile': AppData('PayPal', Icons.payment, AppColors.primary),
    'com.mercadolibre': AppData('MercadoLibre', Icons.store, AppColors.warning),
    
    // Salud y Fitness
    'com.fitbit.FitbitMobile': AppData('Fitbit', Icons.fitness_center, AppColors.success),
    'com.samsung.android.app.shealth': AppData('Samsung Health', Icons.favorite, AppColors.error),
    'com.google.android.apps.fitness': AppData('Google Fit', Icons.directions_run, AppColors.success),
    
    // Utilidades
    'com.google.android.calculator': AppData('Calculadora', Icons.calculate, AppColors.textPrimary),
    'com.android.chrome': AppData('Chrome', Icons.web, AppColors.primary),
    'com.google.android.apps.photos': AppData('Fotos', Icons.photo_library, AppColors.success),
    'com.android.settings': AppData('Configuración', Icons.settings, AppColors.textSecondary),
    
    // Apps del sistema Android comunes
    'com.android.dialer': AppData('Teléfono', Icons.phone, AppColors.success),
    'com.android.mms': AppData('Mensajes', Icons.sms, AppColors.primary),
    'com.android.camera2': AppData('Cámara', Icons.camera_alt, AppColors.info),
    'com.android.contacts': AppData('Contactos', Icons.contacts, AppColors.accent),
    'com.android.calendar': AppData('Calendario', Icons.calendar_today, AppColors.error),
    'com.android.email': AppData('Email', Icons.email, AppColors.primary),
    'com.android.music': AppData('Música', Icons.music_note, AppColors.accent),
    'com.android.gallery3d': AppData('Galería', Icons.photo, AppColors.success),
  };

  /// Obtener todas las aplicaciones instaladas
  Future<List<AppItem>> getInstalledApps() async {
    try {
      print('🔍 Escaneando aplicaciones instaladas...');
      
      // Obtener apps instaladas del sistema
      final apps = await InstalledApps.getInstalledApps(true, true);
      
      _installedApps = apps.map((app) {
        // Buscar en apps populares primero
        final popularApp = _popularApps[app.packageName];
        
        if (popularApp != null) {
          return AppItem(
            name: popularApp.name,
            packageName: app.packageName ?? '',
            icon: popularApp.icon,
            color: popularApp.color,
            isSystemApp: app.systemApp ?? false,
            appIcon: app.icon, // Icono real de la app
          );
        } else {
          // App no popular, usar datos del sistema
          return AppItem(
            name: app.name ?? 'App Desconocida',
            packageName: app.packageName ?? '',
            icon: Icons.android, // Icono genérico
            color: AppColors.textSecondary,
            isSystemApp: app.systemApp ?? false,
            appIcon: app.icon,
          );
        }
      }).where((app) => 
        app.packageName.isNotEmpty && 
        !app.isSystemApp && // Filtrar apps del sistema
        !app.packageName.startsWith('com.android.') ||
        _popularApps.containsKey(app.packageName) // Excepto las populares
      ).toList();

      // Ordenar por popularidad (apps conocidas primero)
      _installedApps.sort((a, b) {
        final aIsPopular = _popularApps.containsKey(a.packageName);
        final bIsPopular = _popularApps.containsKey(b.packageName);
        
        if (aIsPopular && !bIsPopular) return -1;
        if (!aIsPopular && bIsPopular) return 1;
        return a.name.compareTo(b.name);
      });

      print('✅ Encontradas ${_installedApps.length} aplicaciones');
      return _installedApps;
      
    } catch (e) {
      print('❌ Error obteniendo apps instaladas: $e');
      return _getDefaultApps();
    }
  }

  /// Obtener aplicaciones favoritas configuradas
  Future<List<AppItem>> getFavoriteApps() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritePackages = prefs.getStringList('favorite_apps') ?? [];
      
      if (favoritePackages.isEmpty) {
        // Primera vez, usar apps por defecto
        return _getDefaultApps();
      }
      
      // Asegurar que tenemos las apps instaladas
      if (_installedApps.isEmpty) {
        await getInstalledApps();
      }
      
      _favoriteApps = favoritePackages
          .map((packageName) => _installedApps.firstWhere(
                (app) => app.packageName == packageName,
                orElse: () => _getDefaultAppByPackage(packageName),
              ))
          .where((app) => app.packageName.isNotEmpty)
          .toList();
      
      return _favoriteApps;
      
    } catch (e) {
      print('❌ Error obteniendo apps favoritas: $e');
      return _getDefaultApps();
    }
  }

  /// Guardar aplicaciones favoritas
  Future<void> saveFavoriteApps(List<AppItem> apps) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final packageNames = apps.map((app) => app.packageName).toList();
      await prefs.setStringList('favorite_apps', packageNames);
      _favoriteApps = apps;
      print('✅ Apps favoritas guardadas: ${packageNames.length}');
    } catch (e) {
      print('❌ Error guardando apps favoritas: $e');
    }
  }

  /// Lanzar una aplicación
  Future<bool> launchApp(String packageName) async {
    try {
      print('🚀 Lanzando app: $packageName');
      
      final result = await LaunchApp.openApp(
        androidPackageName: packageName,
        iosUrlScheme: '', // No usado en Android
        appStoreLink: '', // No usado para apps instaladas
      );
      
      if (result) {
        print('✅ App lanzada exitosamente: $packageName');
      } else {
        print('❌ No se pudo lanzar la app: $packageName');
      }
      
      return result;
      
    } catch (e) {
      print('❌ Error lanzando app $packageName: $e');
      return false;
    }
  }

  /// Apps por defecto para personas mayores
  List<AppItem> _getDefaultApps() {
    return [
      AppItem(
        name: 'Teléfono',
        packageName: 'com.android.dialer',
        icon: Icons.phone,
        color: AppColors.success,
      ),
      AppItem(
        name: 'Mensajes',
        packageName: 'com.android.mms',
        icon: Icons.sms,
        color: AppColors.primary,
      ),
      AppItem(
        name: 'Cámara',
        packageName: 'com.android.camera2',
        icon: Icons.camera_alt,
        color: AppColors.info,
      ),
      AppItem(
        name: 'WhatsApp',
        packageName: 'com.whatsapp',
        icon: Icons.chat,
        color: AppColors.success,
      ),
      AppItem(
        name: 'YouTube',
        packageName: 'com.google.android.youtube',
        icon: Icons.play_circle_fill,
        color: AppColors.error,
      ),
      AppItem(
        name: 'Gmail',
        packageName: 'com.google.android.gm',
        icon: Icons.mail,
        color: AppColors.error,
      ),
      AppItem(
        name: 'Contactos',
        packageName: 'com.android.contacts',
        icon: Icons.contacts,
        color: AppColors.accent,
      ),
      AppItem(
        name: 'Configuración',
        packageName: 'com.android.settings',
        icon: Icons.settings,
        color: AppColors.textSecondary,
      ),
    ];
  }

  /// Obtener app por defecto por package name
  AppItem _getDefaultAppByPackage(String packageName) {
    final appData = _popularApps[packageName];
    if (appData != null) {
      return AppItem(
        name: appData.name,
        packageName: packageName,
        icon: appData.icon,
        color: appData.color,
      );
    }
    
    return AppItem(
      name: 'App Desconocida',
      packageName: packageName,
      icon: Icons.android,
      color: AppColors.textSecondary,
    );
  }
}

/// Datos de una aplicación popular
class AppData {
  final String name;
  final IconData icon;
  final Color color;
  
  const AppData(this.name, this.icon, this.color);
}
