import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';

/// Tema principal de la aplicación optimizado para personas mayores
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
        primary: AppColors.primary,
        secondary: AppColors.accent,
        surface: AppColors.surface,
        error: AppColors.error,
      ),
      
      // Configuración de fuentes - Grandes y legibles
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: AppDimensions.fontTitle,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
          height: 1.2,
        ),
        displayMedium: TextStyle(
          fontSize: AppDimensions.fontXXL,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.3,
        ),
        displaySmall: TextStyle(
          fontSize: AppDimensions.fontXL,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
          height: 1.3,
        ),
        headlineLarge: TextStyle(
          fontSize: AppDimensions.fontXL,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
          height: 1.3,
        ),
        headlineMedium: TextStyle(
          fontSize: AppDimensions.fontL,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        headlineSmall: TextStyle(
          fontSize: AppDimensions.fontM,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        titleLarge: TextStyle(
          fontSize: AppDimensions.fontL,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        titleMedium: TextStyle(
          fontSize: AppDimensions.fontM,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        titleSmall: TextStyle(
          fontSize: AppDimensions.fontS,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary,
          height: 1.4,
        ),
        bodyLarge: TextStyle(
          fontSize: AppDimensions.fontM,
          fontWeight: FontWeight.normal,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: AppDimensions.fontS,
          fontWeight: FontWeight.normal,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
        bodySmall: TextStyle(
          fontSize: AppDimensions.fontXS,
          fontWeight: FontWeight.normal,
          color: AppColors.textSecondary,
          height: 1.5,
        ),
        labelLarge: TextStyle(
          fontSize: AppDimensions.fontM,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        labelMedium: TextStyle(
          fontSize: AppDimensions.fontS,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary,
          height: 1.4,
        ),
        labelSmall: TextStyle(
          fontSize: AppDimensions.fontXS,
          fontWeight: FontWeight.w500,
          color: AppColors.textLight,
          height: 1.4,
        ),
      ),
      
      // Configuración de botones - Grandes y accesibles
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonPrimary,
          foregroundColor: Colors.white,
          minimumSize: const Size(
            AppDimensions.buttonWidth,
            AppDimensions.buttonHeight,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          elevation: AppDimensions.elevationM,
          textStyle: const TextStyle(
            fontSize: AppDimensions.fontM,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Configuración de botones de texto
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          minimumSize: const Size(
            AppDimensions.buttonWidth,
            AppDimensions.buttonHeight,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          textStyle: const TextStyle(
            fontSize: AppDimensions.fontM,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Configuración de botones outlined
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          backgroundColor: AppColors.surface,
          minimumSize: const Size(
            AppDimensions.buttonWidth,
            AppDimensions.buttonHeight,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          side: const BorderSide(
            color: AppColors.primary,
            width: 2.0,
          ),
          textStyle: const TextStyle(
            fontSize: AppDimensions.fontM,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Configuración de cards
      cardTheme: CardThemeData(
        color: AppColors.surface,
        elevation: AppDimensions.elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        margin: const EdgeInsets.all(AppDimensions.marginS),
      ),
      
      // Configuración de app bar
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: AppDimensions.elevationM,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: AppDimensions.fontL,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      
      // Configuración de iconos
      iconTheme: const IconThemeData(
        color: AppColors.primary,
        size: AppDimensions.iconL,
      ),
      
      // Configuración de input decoration
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          borderSide: const BorderSide(color: AppColors.primary, width: 2.0),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        ),
        labelStyle: const TextStyle(
          fontSize: AppDimensions.fontM,
          color: AppColors.textSecondary,
        ),
        hintStyle: const TextStyle(
          fontSize: AppDimensions.fontM,
          color: AppColors.textLight,
        ),
      ),
      
      // Configuración de diálogos
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.surface,
        elevation: AppDimensions.elevationXL,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        titleTextStyle: const TextStyle(
          fontSize: AppDimensions.fontL,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        contentTextStyle: const TextStyle(
          fontSize: AppDimensions.fontM,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
      ),
      
      // Configuración de bottom navigation bar
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textLight,
        selectedLabelStyle: TextStyle(
          fontSize: AppDimensions.fontS,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: AppDimensions.fontS,
          fontWeight: FontWeight.normal,
        ),
        type: BottomNavigationBarType.fixed,
        elevation: AppDimensions.elevationM,
      ),
      
      // Configuración de floating action button
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.accent,
        foregroundColor: Colors.white,
        elevation: AppDimensions.elevationL,
        iconSize: AppDimensions.iconL,
      ),
    );
  }
  
  // Tema oscuro navy elegante
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.dark,
        primary: AppColors.primary,
        secondary: AppColors.accent,
        surface: AppColors.surface,

        error: AppColors.error,
      ),

      // Configuración de fuentes para tema oscuro
      textTheme: lightTheme.textTheme.copyWith(
        displayLarge: lightTheme.textTheme.displayLarge?.copyWith(
          color: AppColors.textPrimary,
        ),
        displayMedium: lightTheme.textTheme.displayMedium?.copyWith(
          color: AppColors.textPrimary,
        ),
        displaySmall: lightTheme.textTheme.displaySmall?.copyWith(
          color: AppColors.textPrimary,
        ),
        headlineLarge: lightTheme.textTheme.headlineLarge?.copyWith(
          color: AppColors.textPrimary,
        ),
        headlineMedium: lightTheme.textTheme.headlineMedium?.copyWith(
          color: AppColors.textPrimary,
        ),
        headlineSmall: lightTheme.textTheme.headlineSmall?.copyWith(
          color: AppColors.textPrimary,
        ),
        titleLarge: lightTheme.textTheme.titleLarge?.copyWith(
          color: AppColors.textPrimary,
        ),
        titleMedium: lightTheme.textTheme.titleMedium?.copyWith(
          color: AppColors.textPrimary,
        ),
        titleSmall: lightTheme.textTheme.titleSmall?.copyWith(
          color: AppColors.textSecondary,
        ),
        bodyLarge: lightTheme.textTheme.bodyLarge?.copyWith(
          color: AppColors.textPrimary,
        ),
        bodyMedium: lightTheme.textTheme.bodyMedium?.copyWith(
          color: AppColors.textPrimary,
        ),
        bodySmall: lightTheme.textTheme.bodySmall?.copyWith(
          color: AppColors.textSecondary,
        ),
        labelLarge: lightTheme.textTheme.labelLarge?.copyWith(
          color: AppColors.textPrimary,
        ),
        labelMedium: lightTheme.textTheme.labelMedium?.copyWith(
          color: AppColors.textSecondary,
        ),
        labelSmall: lightTheme.textTheme.labelSmall?.copyWith(
          color: AppColors.textLight,
        ),
      ),

      // Configuración de botones para tema oscuro
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          minimumSize: const Size(
            AppDimensions.buttonWidth,
            AppDimensions.buttonHeight,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingL,
            vertical: AppDimensions.paddingM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          elevation: AppDimensions.elevationM,
          textStyle: const TextStyle(
            fontSize: AppDimensions.fontM,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // App bar para tema oscuro
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: AppDimensions.elevationM,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: AppDimensions.fontL,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),

      // Cards para tema oscuro
      cardTheme: CardThemeData(
        color: AppColors.surface,
        elevation: AppDimensions.elevationM,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        margin: const EdgeInsets.all(AppDimensions.marginS),
      ),

      // Iconos para tema oscuro
      iconTheme: const IconThemeData(
        color: AppColors.accent,
        size: AppDimensions.iconL,
      ),
    );
  }

  // Tema para el modo administrador
  static ThemeData get adminTheme {
    return darkTheme.copyWith(
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.adminPrimary,
        brightness: Brightness.dark,
        primary: AppColors.adminPrimary,
        secondary: AppColors.adminSecondary,
        surface: AppColors.adminBackground,

      ),
      appBarTheme: darkTheme.appBarTheme.copyWith(
        backgroundColor: AppColors.adminPrimary,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: darkTheme.elevatedButtonTheme.style?.copyWith(
          backgroundColor: WidgetStateProperty.all(AppColors.adminPrimary),
        ),
      ),
    );
  }
}
