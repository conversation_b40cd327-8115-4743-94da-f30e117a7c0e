import 'package:flutter/services.dart';
import 'dart:async';

/// Servicio para manejar el "Kiosk Handoff" - Transferencia inteligente entre apps en modo kiosko
/// Esta estrategia permite que las apps se abran normalmente desactivando temporalmente
/// el Lock Task Mode y reactivándolo inmediatamente en la app destino
class KioskHandoffService {
  static const MethodChannel _channel = MethodChannel('senior_launcher/kiosk_handoff');
  
  static KioskHandoffService? _instance;
  static KioskHandoffService get instance => _instance ??= KioskHandoffService._();
  
  KioskHandoffService._();

  bool _isHandoffInProgress = false;
  Timer? _handoffTimer;

  /// Lanzar app con Kiosk Handoff - La estrategia inteligente
  Future<bool> launchAppWithHandoff(String packageName, String appName) async {
    if (_isHandoffInProgress) {
      print('⚠️ Handoff ya en progreso, ignorando...');
      return false;
    }

    try {
      print('🔄 Iniciando Kiosk Handoff para $appName ($packageName)');
      _isHandoffInProgress = true;

      // Paso 1: Desactivar Lock Task Mode temporalmente
      print('🔓 Paso 1: Desactivando Lock Task Mode temporalmente...');
      final unlockSuccess = await _channel.invokeMethod('temporaryUnlock');
      
      if (!unlockSuccess) {
        print('❌ Error desactivando Lock Task Mode temporalmente');
        _isHandoffInProgress = false;
        return false;
      }

      // Paso 2: Pequeña pausa para asegurar que el unlock se procese
      await Future.delayed(const Duration(milliseconds: 100));

      // Paso 3: Lanzar la aplicación destino
      print('🚀 Paso 2: Lanzando $appName...');
      final launchSuccess = await _channel.invokeMethod('launchAppForHandoff', {
        'packageName': packageName,
        'appName': appName,
      });

      if (!launchSuccess) {
        print('❌ Error lanzando $appName');
        // Reactivar Lock Task Mode en el launcher
        await _reactivateLauncherLockTask();
        _isHandoffInProgress = false;
        return false;
      }

      // Paso 4: Activar Lock Task Mode en la app destino (con delay)
      print('🔒 Paso 3: Programando reactivación de Lock Task Mode...');
      _scheduleTargetAppLockTask(packageName, appName);

      return true;

    } catch (e) {
      print('❌ Error en Kiosk Handoff: $e');
      await _reactivateLauncherLockTask();
      _isHandoffInProgress = false;
      return false;
    }
  }

  /// Programar la activación de Lock Task Mode en la app destino
  void _scheduleTargetAppLockTask(String packageName, String appName) {
    // Cancelar timer anterior si existe
    _handoffTimer?.cancel();

    // Programar activación después de que la app se abra completamente
    _handoffTimer = Timer(const Duration(milliseconds: 1500), () async {
      try {
        print('🔒 Activando Lock Task Mode en $appName...');
        
        final success = await _channel.invokeMethod('activateTargetAppLockTask', {
          'packageName': packageName,
        });

        if (success) {
          print('✅ Lock Task Mode activado en $appName');
        } else {
          print('⚠️ No se pudo activar Lock Task Mode en $appName (puede ser normal)');
        }

      } catch (e) {
        print('❌ Error activando Lock Task Mode en $appName: $e');
      } finally {
        _isHandoffInProgress = false;
      }
    });
  }

  /// Reactivar Lock Task Mode en el launcher
  Future<void> _reactivateLauncherLockTask() async {
    try {
      print('🔒 Reactivando Lock Task Mode en Senior Launcher...');
      
      final success = await _channel.invokeMethod('reactivateLauncherLockTask');
      
      if (success) {
        print('✅ Lock Task Mode reactivado en Senior Launcher');
      } else {
        print('❌ Error reactivando Lock Task Mode en Senior Launcher');
      }

    } catch (e) {
      print('❌ Error reactivando Lock Task Mode: $e');
    }
  }

  /// Manejar el regreso al launcher desde una app externa
  Future<void> handleReturnToLauncher() async {
    try {
      print('🏠 App externa cerrada, regresando al launcher...');
      
      // Cancelar cualquier timer pendiente
      _handoffTimer?.cancel();
      
      // Asegurar que el launcher esté en Lock Task Mode
      await _reactivateLauncherLockTask();
      
      _isHandoffInProgress = false;
      
      print('✅ Regreso al launcher completado');

    } catch (e) {
      print('❌ Error manejando regreso al launcher: $e');
    }
  }

  /// Verificar si una app está permitida para Handoff
  Future<bool> isAppAllowedForHandoff(String packageName) async {
    try {
      final result = await _channel.invokeMethod('isAppAllowedForHandoff', {
        'packageName': packageName,
      });
      
      return result == true;
      
    } catch (e) {
      print('❌ Error verificando app permitida: $e');
      return false;
    }
  }

  /// Configurar apps permitidas para Handoff
  Future<bool> configureHandoffApps() async {
    try {
      print('🔧 Configurando apps permitidas para Handoff...');
      
      // Lista de apps que soportan Kiosk Handoff
      final handoffApps = [
        'com.seniorlauncher.senior_launcher', // Nuestro launcher
        'com.google.android.youtube',         // YouTube
        'com.google.android.gm',              // Gmail
        'com.whatsapp',                       // WhatsApp
        'com.facebook.katana',                // Facebook
        'com.instagram.android',              // Instagram
        'com.netflix.mediaclient',            // Netflix
        'com.spotify.music',                  // Spotify
        'com.google.android.apps.maps',       // Google Maps
        'com.android.chrome',                 // Chrome
        'com.android.dialer',                 // Teléfono
        'com.android.camera2',                // Cámara
        'com.android.mms',                    // Mensajes
        'com.android.contacts',               // Contactos
        'com.android.settings',               // Configuración
        'com.google.android.calculator',      // Calculadora
        'com.google.android.apps.photos',     // Google Fotos
      ];

      final result = await _channel.invokeMethod('configureHandoffApps', {
        'packages': handoffApps,
      });

      if (result == true) {
        print('✅ Apps de Handoff configuradas correctamente');
        return true;
      } else {
        print('❌ Error configurando apps de Handoff');
        return false;
      }
      
    } catch (e) {
      print('❌ Error en configureHandoffApps: $e');
      return false;
    }
  }

  /// Inicializar el sistema de Kiosk Handoff
  Future<bool> initializeHandoffSystem() async {
    try {
      print('🚀 Inicializando sistema de Kiosk Handoff...');
      
      // 1. Configurar apps permitidas
      final appsConfigured = await configureHandoffApps();
      if (!appsConfigured) {
        print('❌ Error configurando apps de Handoff');
        return false;
      }

      // 2. Inicializar el sistema en Android
      final systemInitialized = await _channel.invokeMethod('initializeHandoffSystem');
      if (!systemInitialized) {
        print('❌ Error inicializando sistema de Handoff');
        return false;
      }

      print('✅ Sistema de Kiosk Handoff inicializado correctamente');
      return true;
      
    } catch (e) {
      print('❌ Error inicializando Handoff: $e');
      return false;
    }
  }

  /// Limpiar recursos del Handoff
  void dispose() {
    _handoffTimer?.cancel();
    _isHandoffInProgress = false;
  }

  /// Verificar si hay un handoff en progreso
  bool get isHandoffInProgress => _isHandoffInProgress;
}
