package com.seniorlauncher.senior_launcher

import android.app.ActivityManager
import android.app.admin.DevicePolicyManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val APP_LAUNCHER_CHANNEL = "senior_launcher/app_launcher"
    private val KIOSK_MODE_CHANNEL = "senior_launcher/kiosk_mode"

    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    private val handler = Handler(Looper.getMainLooper())

    // Broadcast receiver para restaurar kiosko
    private val kioskRestoreReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.seniorlauncher.RESTORE_KIOSK") {
                handler.postDelayed({
                    enableKioskMode()
                }, 1000L)
            }
        }
    }

    override fun onCreate(savedInstanceState: android.os.Bundle?) {
        super.onCreate(savedInstanceState)

        // Registrar broadcast receiver para restauración de kiosko
        val filter = IntentFilter("com.seniorlauncher.RESTORE_KIOSK")
        registerReceiver(kioskRestoreReceiver, filter, Context.RECEIVER_NOT_EXPORTED)

        // Verificar si se debe restaurar automáticamente
        val autoRestore = intent?.getBooleanExtra("auto_restore_kiosk", false) ?: false
        if (autoRestore) {
            handler.postDelayed({
                val prefs = getSharedPreferences("senior_launcher_prefs", Context.MODE_PRIVATE)
                val wasKioskActive = prefs.getBoolean("kiosk_mode_active", false)
                if (wasKioskActive) {
                    enableKioskMode()
                }
            }, 2000L)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            unregisterReceiver(kioskRestoreReceiver)
        } catch (e: Exception) {
            // Receiver ya no registrado
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Inicializar Device Policy Manager
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, SeniorLauncherDeviceAdminReceiver::class.java)

        // Channel para lanzamiento de aplicaciones
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, APP_LAUNCHER_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchApp" -> {
                    val packageName = call.argument<String>("packageName")
                    if (packageName != null) {
                        val success = launchApp(packageName)
                        result.success(success)
                    } else {
                        result.error("INVALID_ARGUMENT", "Package name is required", null)
                    }
                }
                "launchIntent" -> {
                    val action = call.argument<String>("action")
                    val category = call.argument<String>("category")
                    val data = call.argument<String>("data")
                    val type = call.argument<String>("type")

                    if (action != null) {
                        val success = launchIntent(action, category, data, type)
                        result.success(success)
                    } else {
                        result.error("INVALID_ARGUMENT", "Action is required", null)
                    }
                }
                "isAppInstalled" -> {
                    val packageName = call.argument<String>("packageName")
                    if (packageName != null) {
                        val isInstalled = isAppInstalled(packageName)
                        result.success(isInstalled)
                    } else {
                        result.error("INVALID_ARGUMENT", "Package name is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Channel para modo kiosko
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, KIOSK_MODE_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "isDeviceAdminEnabled" -> {
                    val isEnabled = devicePolicyManager.isAdminActive(adminComponent)
                    result.success(isEnabled)
                }
                "requestDeviceAdminPermission" -> {
                    requestDeviceAdminPermission()
                    result.success(true)
                }
                "enableKioskMode" -> {
                    val success = enableKioskMode()
                    result.success(success)
                }
                "disableKioskMode" -> {
                    val success = disableKioskMode()
                    result.success(success)
                }
                "isKioskModeActive" -> {
                    val isActive = isKioskModeActive()
                    result.success(isActive)
                }
                "setAsDefaultLauncher" -> {
                    setAsDefaultLauncher()
                    result.success(true)
                }
                "isDefaultLauncher" -> {
                    val isDefault = isDefaultLauncher()
                    result.success(isDefault)
                }
                "disableHomeButton" -> {
                    val success = disableHomeButton()
                    result.success(success)
                }
                "enableHomeButton" -> {
                    val success = enableHomeButton()
                    result.success(success)
                }
                "disableStatusBar" -> {
                    val success = disableStatusBar()
                    result.success(success)
                }
                "enableStatusBar" -> {
                    val success = enableStatusBar()
                    result.success(success)
                }
                "getAllowedApps" -> {
                    val allowedApps = getAllowedApps()
                    result.success(allowedApps)
                }
                "isAppAllowed" -> {
                    val packageName = call.argument<String>("packageName") ?: ""
                    val isAllowed = isAppAllowed(packageName)
                    result.success(isAllowed)
                }
                "isDeviceOwner" -> {
                    val isOwner = isDeviceOwner()
                    result.success(isOwner)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Canal para Kiosk Handoff - La estrategia inteligente
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "senior_launcher/kiosk_handoff").setMethodCallHandler { call, result ->
            when (call.method) {
                "temporaryUnlock" -> {
                    val success = temporaryUnlockKiosk()
                    result.success(success)
                }
                "launchAppForHandoff" -> {
                    val packageName = call.argument<String>("packageName") ?: ""
                    val appName = call.argument<String>("appName") ?: ""
                    val success = launchAppForHandoff(packageName, appName)
                    result.success(success)
                }
                "activateTargetAppLockTask" -> {
                    val packageName = call.argument<String>("packageName") ?: ""
                    val success = activateTargetAppLockTask(packageName)
                    result.success(success)
                }
                "reactivateLauncherLockTask" -> {
                    val success = reactivateLauncherLockTask()
                    result.success(success)
                }
                "isAppAllowedForHandoff" -> {
                    val packageName = call.argument<String>("packageName") ?: ""
                    val allowed = isAppAllowedForHandoff(packageName)
                    result.success(allowed)
                }
                "configureHandoffApps" -> {
                    val packages = call.argument<List<String>>("packages") ?: emptyList()
                    val success = configureHandoffApps(packages)
                    result.success(success)
                }
                "initializeHandoffSystem" -> {
                    val success = initializeHandoffSystem()
                    result.success(success)
                }
                else -> result.notImplemented()
            }
        }
    }

    private fun launchApp(packageName: String): Boolean {
        return try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun launchIntent(action: String, category: String?, data: String?, type: String?): Boolean {
        return try {
            val intent = Intent(action)

            category?.let { intent.addCategory(it) }
            data?.let { intent.data = Uri.parse(it) }
            type?.let { intent.type = it }

            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    // Métodos para modo kiosko
    private fun requestDeviceAdminPermission() {
        val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN)
        intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminComponent)
        intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION,
            "Senior Launcher necesita permisos de administrador para el modo kiosko")
        startActivity(intent)
    }

    private fun enableKioskMode(): Boolean {
        return try {
            if (devicePolicyManager.isAdminActive(adminComponent)) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    // Configurar apps permitidas en modo kiosko
                    setupLockTaskPackages()

                    startLockTask()

                    // Guardar estado del modo kiosko
                    val prefs = getSharedPreferences("senior_launcher_prefs", Context.MODE_PRIVATE)
                    prefs.edit().putBoolean("kiosk_mode_active", true).apply()

                    true
                } else {
                    false
                }
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun disableKioskMode(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                stopLockTask()

                // Guardar estado del modo kiosko
                val prefs = getSharedPreferences("senior_launcher_prefs", Context.MODE_PRIVATE)
                prefs.edit().putBoolean("kiosk_mode_active", false).apply()

                true
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun isKioskModeActive(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            activityManager.lockTaskModeState != ActivityManager.LOCK_TASK_MODE_NONE
        } else {
            false
        }
    }

    private fun setAsDefaultLauncher() {
        // Guardar que se está configurando como launcher por defecto
        val prefs = getSharedPreferences("senior_launcher_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("is_default_launcher", true).apply()

        val intent = Intent(Settings.ACTION_HOME_SETTINGS)
        startActivity(intent)
    }

    private fun isDefaultLauncher(): Boolean {
        val intent = Intent(Intent.ACTION_MAIN)
        intent.addCategory(Intent.CATEGORY_HOME)
        val resolveInfo = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
        return resolveInfo?.activityInfo?.packageName == packageName
    }

    private fun disableHomeButton(): Boolean {
        return try {
            // Esto requiere permisos especiales o ser una app del sistema
            // En modo kiosko normal, el botón home ya está bloqueado
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun enableHomeButton(): Boolean {
        return try {
            // Restaurar funcionalidad del botón home
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun disableStatusBar(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun enableStatusBar(): Boolean {
        return try {
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun setupLockTaskPackages() {
        try {
            if (devicePolicyManager.isAdminActive(adminComponent)) {
                // Verificar si somos Device Owner (permisos completos)
                val isDeviceOwner = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                    devicePolicyManager.isDeviceOwnerApp(packageName)
                } else {
                    false
                }

                android.util.Log.d("MainActivity", "Device Admin activo: true, Device Owner: $isDeviceOwner")

                if (isDeviceOwner) {
                    // Configuración completa con Device Owner
                    setupFullLockTaskPackages()
                } else {
                    // Configuración básica sin Device Owner
                    android.util.Log.w("MainActivity", "Sin permisos de Device Owner - usando Lock Task Mode básico")
                    android.util.Log.i("MainActivity", "Para Lock Task Mode inteligente, configure como Device Owner con ADB")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error configurando lock task packages", e)
        }
    }

    private fun setupFullLockTaskPackages() {
        try {
            // Lista de apps permitidas en modo kiosko - Ampliada para mayor compatibilidad
            val allowedPackages = arrayOf(
                packageName, // Senior Launcher

                // Teléfono - Múltiples variantes
                "com.android.dialer",
                "com.google.android.dialer",
                "com.samsung.android.dialer",
                "com.sec.android.app.dialertab",
                "com.android.phone",

                // Cámara - Múltiples variantes
                "com.android.camera2",
                "com.android.camera",
                "com.google.android.GoogleCamera",
                "com.samsung.android.camera",
                "com.sec.android.app.camera",

                // Contactos
                "com.android.contacts",
                "com.google.android.contacts",
                "com.samsung.android.contacts",

                // Mensajes
                "com.android.mms",
                "com.google.android.apps.messaging",
                "com.samsung.android.messaging",

                // Emergencia
                "com.android.emergency",
                "com.sec.android.app.safetyassurance",

                // Apps populares - YouTube y otras
                "com.google.android.youtube",
                "com.google.android.gm",
                "com.whatsapp",
                "com.facebook.katana",
                "com.instagram.android",
                "com.netflix.mediaclient",
                "com.spotify.music",
                "com.google.android.apps.maps",
                "com.android.chrome",
                "com.google.android.apps.photos",

                // Configuraciones (limitado)
                "com.android.settings",
                "com.samsung.android.settings"
            )

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                devicePolicyManager.setLockTaskPackages(adminComponent, allowedPackages)
                android.util.Log.d("MainActivity", "Lock task packages configurados: ${allowedPackages.size} apps")
                android.util.Log.d("MainActivity", "Apps permitidas: ${allowedPackages.joinToString()}")
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error configurando lock task packages completos", e)
        }
    }

    private fun getAllowedApps(): List<String> {
        return try {
            if (devicePolicyManager.isAdminActive(adminComponent) && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val packages = devicePolicyManager.getLockTaskPackages(adminComponent)
                packages?.toList() ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error obteniendo apps permitidas", e)
            emptyList()
        }
    }

    private fun isAppAllowed(packageName: String): Boolean {
        return try {
            val allowedApps = getAllowedApps()
            allowedApps.contains(packageName)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error verificando app permitida", e)
            false
        }
    }

    private fun isDeviceOwner(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                devicePolicyManager.isDeviceOwnerApp(packageName)
            } else {
                false
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error verificando Device Owner", e)
            false
        }
    }

    // ==================== KIOSK HANDOFF METHODS ====================
    // Métodos para la estrategia de Kiosk Handoff

    private fun temporaryUnlockKiosk(): Boolean {
        return try {
            android.util.Log.d("KioskHandoff", "🔓 Desactivando Lock Task Mode temporalmente...")

            if (isKioskModeActive()) {
                stopLockTask()
                android.util.Log.d("KioskHandoff", "✅ Lock Task Mode desactivado temporalmente")
                return true
            } else {
                android.util.Log.d("KioskHandoff", "ℹ️ No estaba en Lock Task Mode")
                return true
            }
        } catch (e: Exception) {
            android.util.Log.e("KioskHandoff", "❌ Error desactivando Lock Task Mode temporalmente", e)
            false
        }
    }

    private fun launchAppForHandoff(packageName: String, appName: String): Boolean {
        return try {
            android.util.Log.d("KioskHandoff", "🚀 Lanzando $appName para Handoff...")

            val intent = packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(intent)
                android.util.Log.d("KioskHandoff", "✅ $appName lanzada exitosamente")
                return true
            } else {
                android.util.Log.e("KioskHandoff", "❌ No se encontró intent para $packageName")
                return false
            }
        } catch (e: Exception) {
            android.util.Log.e("KioskHandoff", "❌ Error lanzando $appName", e)
            false
        }
    }

    private fun activateTargetAppLockTask(packageName: String): Boolean {
        return try {
            android.util.Log.d("KioskHandoff", "🔒 Activando Lock Task Mode en $packageName...")
            // En esta implementación, simplemente reportamos éxito
            // La app destino debe manejar su propio Lock Task Mode
            android.util.Log.d("KioskHandoff", "✅ Handoff completado para $packageName")
            return true
        } catch (e: Exception) {
            android.util.Log.e("KioskHandoff", "❌ Error en activateTargetAppLockTask", e)
            false
        }
    }

    private fun reactivateLauncherLockTask(): Boolean {
        return try {
            android.util.Log.d("KioskHandoff", "🔒 Reactivando Lock Task Mode en Senior Launcher...")

            if (!isKioskModeActive()) {
                startLockTask()
                android.util.Log.d("KioskHandoff", "✅ Lock Task Mode reactivado en Senior Launcher")
                return true
            } else {
                android.util.Log.d("KioskHandoff", "ℹ️ Ya estaba en Lock Task Mode")
                return true
            }
        } catch (e: Exception) {
            android.util.Log.e("KioskHandoff", "❌ Error reactivando Lock Task Mode", e)
            false
        }
    }

    private fun isAppAllowedForHandoff(packageName: String): Boolean {
        val allowedApps = listOf(
            "com.google.android.youtube",
            "com.google.android.gm",
            "com.whatsapp",
            "com.facebook.katana",
            "com.instagram.android",
            "com.netflix.mediaclient",
            "com.spotify.music",
            "com.google.android.apps.maps",
            "com.android.chrome",
            "com.android.dialer",
            "com.android.camera2",
            "com.android.mms",
            "com.android.contacts",
            "com.android.settings"
        )

        return allowedApps.contains(packageName)
    }

    private fun configureHandoffApps(packages: List<String>): Boolean {
        return try {
            android.util.Log.d("KioskHandoff", "🔧 Configurando ${packages.size} apps para Handoff...")

            if (devicePolicyManager.isAdminActive(adminComponent) && isDeviceOwner()) {
                // Configurar las apps permitidas en Lock Task Mode
                devicePolicyManager.setLockTaskPackages(adminComponent, packages.toTypedArray())
                android.util.Log.d("KioskHandoff", "✅ Apps de Handoff configuradas correctamente")
                return true
            } else {
                android.util.Log.w("KioskHandoff", "⚠️ Sin permisos de Device Owner para configurar Handoff")
                return false
            }
        } catch (e: Exception) {
            android.util.Log.e("KioskHandoff", "❌ Error configurando apps de Handoff", e)
            false
        }
    }

    private fun initializeHandoffSystem(): Boolean {
        return try {
            android.util.Log.d("KioskHandoff", "🚀 Inicializando sistema de Kiosk Handoff...")

            if (devicePolicyManager.isAdminActive(adminComponent) && isDeviceOwner()) {
                // Configurar el sistema para Handoff
                setupLockTaskPackages()
                android.util.Log.d("KioskHandoff", "✅ Sistema de Kiosk Handoff inicializado")
                return true
            } else {
                android.util.Log.w("KioskHandoff", "⚠️ Sin permisos para inicializar Handoff")
                return false
            }
        } catch (e: Exception) {
            android.util.Log.e("KioskHandoff", "❌ Error inicializando sistema de Handoff", e)
            false
        }
    }

    override fun onBackPressed() {
        // En modo kiosko, bloquear el botón de retroceso
        if (isKioskModeActive()) {
            // No hacer nada - bloquear el botón back
            return
        }
        super.onBackPressed()
    }
}
