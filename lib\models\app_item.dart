import 'package:flutter/material.dart';
import 'dart:typed_data';

/// Modelo que representa una aplicación en el launcher
class AppItem {
  final String name;
  final String packageName;
  final IconData icon;
  final Color color;
  final String? description;
  final bool isFavorite;
  final bool isSystemApp;
  final DateTime? lastUsed;
  final Uint8List? appIcon; // Icono real de la aplicación

  const AppItem({
    required this.name,
    required this.packageName,
    required this.icon,
    required this.color,
    this.description,
    this.isFavorite = false,
    this.isSystemApp = false,
    this.lastUsed,
    this.appIcon,
  });

  /// Crea una copia del AppItem con los campos especificados modificados
  AppItem copyWith({
    String? name,
    String? packageName,
    IconData? icon,
    Color? color,
    String? description,
    bool? isFavorite,
    bool? isSystemApp,
    DateTime? lastUsed,
  }) {
    return AppItem(
      name: name ?? this.name,
      packageName: packageName ?? this.packageName,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      description: description ?? this.description,
      isFavorite: isFavorite ?? this.isFavorite,
      isSystemApp: isSystemApp ?? this.isSystemApp,
      lastUsed: lastUsed ?? this.lastUsed,
    );
  }

  /// Convierte el AppItem a un Map para almacenamiento
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'packageName': packageName,
      'icon': icon.codePoint,
      'color': color.toARGB32(),
      'description': description,
      'isFavorite': isFavorite,
      'isSystemApp': isSystemApp,
      'lastUsed': lastUsed?.millisecondsSinceEpoch,
    };
  }

  /// Crea un AppItem desde un Map
  factory AppItem.fromMap(Map<String, dynamic> map) {
    return AppItem(
      name: map['name'] ?? '',
      packageName: map['packageName'] ?? '',
      icon: IconData(map['icon'] ?? Icons.apps.codePoint, fontFamily: 'MaterialIcons'),
      color: Color(map['color'] ?? Colors.blue.toARGB32()),
      description: map['description'],
      isFavorite: map['isFavorite'] ?? false,
      isSystemApp: map['isSystemApp'] ?? false,
      lastUsed: map['lastUsed'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['lastUsed'])
          : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppItem && other.packageName == packageName;
  }

  @override
  int get hashCode => packageName.hashCode;

  @override
  String toString() {
    return 'AppItem(name: $name, packageName: $packageName, isFavorite: $isFavorite)';
  }
}
