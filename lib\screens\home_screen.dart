import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../constants/app_strings.dart';
import '../widgets/greeting_widget.dart';
import '../widgets/quick_settings_panel.dart';
import '../widgets/apps_grid.dart';
import '../widgets/status_bar_widget.dart';
import '../services/app_launcher_service.dart';
import 'admin_screen.dart';

/// Pantalla principal del launcher orientado a personas mayores
/// Diseñada con botones grandes, colores suaves y navegación simplificada
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Contador para acceso al modo administrador (toque 7 veces en el logo)
  int _adminTapCount = 0;
  DateTime? _lastTap;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Barra de estado personalizada
              const StatusBarWidget(),

              // Contenido principal
              Expanded(
                child: _buildMainContent(),
              ),

              // Panel de configuraciones rápidas
              const QuickSettingsPanel(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingS),
      child: Column(
        children: [
          // Saludo personalizado con acceso oculto al modo admin
          GestureDetector(
            onTap: _handleAdminAccess,
            child: const GreetingWidget(),
          ),

          const SizedBox(height: AppDimensions.paddingS),

          // Grilla de aplicaciones principales
          Expanded(
            child: AppsGrid(),
          ),
        ],
      ),
    );
  }

  Future<void> _handleAppTap(String appPackage) async {
    // Mostrar indicador de carga
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Abriendo $appPackage...'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        duration: const Duration(seconds: 1),
      ),
    );

    // Manejar casos especiales
    if (appPackage == 'help') {
      _showHelpDialog();
      return;
    }

    // Intentar lanzar la aplicación
    final success = await AppLauncherService.instance.launchApp(appPackage);

    if (!success) {
      // Mostrar error si no se pudo abrir
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('No se pudo abrir la aplicación'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          ),
        );
      }
    }
  }

  void _handleAdminAccess() {
    final now = DateTime.now();
    
    // Resetear contador si han pasado más de 3 segundos
    if (_lastTap == null || now.difference(_lastTap!).inSeconds > 3) {
      _adminTapCount = 1;
    } else {
      _adminTapCount++;
    }
    
    _lastTap = now;
    
    // Acceso al modo administrador después de 7 toques
    if (_adminTapCount >= 7) {
      _showAdminPinDialog();
      _adminTapCount = 0;
    }
  }

  void _showAdminPinDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        title: const Text(
          AppStrings.adminMode,
          style: TextStyle(
            fontSize: AppDimensions.fontL,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              AppStrings.enterPin,
              style: TextStyle(fontSize: AppDimensions.fontM),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            TextField(
              obscureText: true,
              keyboardType: TextInputType.number,
              maxLength: 4,
              style: const TextStyle(
                fontSize: AppDimensions.fontL,
                letterSpacing: 8.0,
              ),
              textAlign: TextAlign.center,
              decoration: const InputDecoration(
                hintText: '••••',
                counterText: '',
              ),
              onSubmitted: _validateAdminPin,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              AppStrings.cancel,
              style: TextStyle(fontSize: AppDimensions.fontM),
            ),
          ),
        ],
      ),
    );
  }

  void _validateAdminPin(String pin) {
    Navigator.of(context).pop();
    
    // PIN por defecto: 1234 (en producción debería ser configurable)
    if (pin == '1234') {
      _openAdminMode();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            AppStrings.wrongPin,
            style: TextStyle(fontSize: AppDimensions.fontM),
          ),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
      );
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          AppStrings.helpTitle,
          style: TextStyle(
            fontSize: AppDimensions.fontL,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                AppStrings.helpDescription,
                style: TextStyle(fontSize: AppDimensions.fontM),
              ),
              const SizedBox(height: AppDimensions.paddingL),
              _buildHelpItem(
                Icons.touch_app,
                'Toque los botones grandes para abrir aplicaciones',
              ),
              _buildHelpItem(
                Icons.volume_up,
                'Use los controles inferiores para ajustar volumen y brillo',
              ),
              _buildHelpItem(
                Icons.help_outline,
                'Para ayuda adicional, contacte a su familiar o técnico',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              AppStrings.ok,
              style: TextStyle(fontSize: AppDimensions.fontM),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: AppDimensions.iconM,
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: AppDimensions.fontS),
            ),
          ),
        ],
      ),
    );
  }

  void _openAdminMode() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AdminScreen(),
      ),
    );
  }
}
