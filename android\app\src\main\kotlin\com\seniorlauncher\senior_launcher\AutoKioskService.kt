package com.seniorlauncher.senior_launcher

import android.app.ActivityManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat

/**
 * Servicio que mantiene activo el modo kiosko y reactiva automáticamente
 * las protecciones después de un reinicio del dispositivo
 */
class AutoKioskService : Service() {
    
    companion object {
        private const val TAG = "AutoKioskService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "senior_launcher_kiosk"
        private const val RESTORE_DELAY = 3000L // 3 segundos de delay
    }
    
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "AutoKioskService creado")
        
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, SeniorLauncherDeviceAdminReceiver::class.java)
        
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "AutoKioskService iniciado")
        
        val restoreKiosk = intent?.getBooleanExtra("restore_kiosk", false) ?: false
        val restoreLauncher = intent?.getBooleanExtra("restore_launcher", false) ?: false
        
        // Iniciar como servicio en primer plano
        startForeground(NOTIFICATION_ID, createNotification())
        
        // Programar la restauración después de un delay
        handler.postDelayed({
            restoreKioskMode(restoreKiosk, restoreLauncher)
        }, RESTORE_DELAY)
        
        // Mantener el servicio activo
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Senior Launcher Kiosko",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Mantiene activo el modo kiosko"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Senior Launcher Activo")
            .setContentText("Modo kiosko protegido")
            .setSmallIcon(android.R.drawable.ic_lock_idle_lock)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setShowWhen(false)
            .build()
    }

    private fun restoreKioskMode(restoreKiosk: Boolean, restoreLauncher: Boolean) {
        Log.d(TAG, "Restaurando configuración - Kiosko: $restoreKiosk, Launcher: $restoreLauncher")
        
        try {
            // 1. Lanzar MainActivity si no está activa
            if (!isMainActivityRunning()) {
                launchMainActivity()
                
                // Esperar un poco para que se lance la actividad
                handler.postDelayed({
                    continueRestore(restoreKiosk)
                }, 2000L)
            } else {
                continueRestore(restoreKiosk)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error restaurando modo kiosko", e)
        }
    }
    
    private fun continueRestore(restoreKiosk: Boolean) {
        try {
            // 2. Restaurar modo kiosko si estaba activo
            if (restoreKiosk && devicePolicyManager.isAdminActive(adminComponent)) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    // Verificar si ya está en lock task mode
                    if (!isLockTaskModeActive()) {
                        // Enviar broadcast a MainActivity para activar kiosko
                        val kioskIntent = Intent("com.seniorlauncher.RESTORE_KIOSK")
                        sendBroadcast(kioskIntent)
                        Log.d(TAG, "Broadcast enviado para restaurar kiosko")
                    } else {
                        Log.d(TAG, "Lock task mode ya está activo")
                    }
                }
            }
            
            // 3. Detener el servicio después de un tiempo
            handler.postDelayed({
                stopSelf()
            }, 10000L) // 10 segundos
            
        } catch (e: Exception) {
            Log.e(TAG, "Error en continueRestore", e)
        }
    }

    private fun isMainActivityRunning(): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = activityManager.getRunningTasks(1)
        
        return if (runningTasks.isNotEmpty()) {
            val topActivity = runningTasks[0].topActivity
            topActivity?.className == MainActivity::class.java.name
        } else {
            false
        }
    }

    private fun launchMainActivity() {
        val launchIntent = Intent(this, MainActivity::class.java)
        launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        launchIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
        launchIntent.putExtra("auto_restore_kiosk", true)
        startActivity(launchIntent)
        Log.d(TAG, "MainActivity lanzada para restauración")
    }

    private fun isLockTaskModeActive(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            activityManager.lockTaskModeState != ActivityManager.LOCK_TASK_MODE_NONE
        } else {
            false
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "AutoKioskService destruido")
        handler.removeCallbacksAndMessages(null)
    }
}
