import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Servicio para mantener la persistencia visual del tema
/// Asegura que el diseño navy se mantenga consistente
class ThemePersistenceService {
  static const MethodChannel _channel = MethodChannel('senior_launcher/theme_persistence');
  
  static ThemePersistenceService? _instance;
  static ThemePersistenceService get instance => _instance ??= ThemePersistenceService._();
  
  ThemePersistenceService._();

  /// Aplica el tema navy a la barra de estado del sistema
  Future<void> applySystemTheme() async {
    try {
      // Configurar barra de estado para tema oscuro
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Color(0xFF0F172A), // Navy oscuro
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
          systemNavigationBarColor: Color(0xFF1E293B), // Slate oscuro
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Color(0xFF06B6D4), // Cyan accent
        ),
      );

      // Configurar orientación
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // Mantener pantalla encendida
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      print('Tema del sistema aplicado correctamente');
    } catch (e) {
      print('Error aplicando tema del sistema: $e');
    }
  }

  /// Aplica el tema inmersivo para modo kiosko
  Future<void> applyKioskTheme() async {
    try {
      // Modo inmersivo completo
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );

      // Barra de estado completamente oculta
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
      );

      print('Tema kiosko aplicado correctamente');
    } catch (e) {
      print('Error aplicando tema kiosko: $e');
    }
  }

  /// Restaura el tema normal
  Future<void> restoreNormalTheme() async {
    try {
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
      );

      await applySystemTheme();
      print('Tema normal restaurado');
    } catch (e) {
      print('Error restaurando tema normal: $e');
    }
  }

  /// Guarda las preferencias de tema
  Future<void> saveThemePreferences({
    required bool isDarkMode,
    required bool isKioskMode,
    required double fontScale,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('theme_dark_mode', isDarkMode);
      await prefs.setBool('theme_kiosk_mode', isKioskMode);
      await prefs.setDouble('theme_font_scale', fontScale);
      print('Preferencias de tema guardadas');
    } catch (e) {
      print('Error guardando preferencias de tema: $e');
    }
  }

  /// Carga las preferencias de tema
  Future<Map<String, dynamic>> loadThemePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'isDarkMode': prefs.getBool('theme_dark_mode') ?? true,
        'isKioskMode': prefs.getBool('theme_kiosk_mode') ?? false,
        'fontScale': prefs.getDouble('theme_font_scale') ?? 1.0,
      };
    } catch (e) {
      print('Error cargando preferencias de tema: $e');
      return {
        'isDarkMode': true,
        'isKioskMode': false,
        'fontScale': 1.0,
      };
    }
  }

  /// Configura el tema para apps externas
  Future<void> configureExternalAppTheme() async {
    try {
      await _channel.invokeMethod('configureExternalAppTheme');
      print('Tema configurado para apps externas');
    } catch (e) {
      print('Error configurando tema para apps externas: $e');
    }
  }

  /// Restaura el tema al regresar de apps externas
  Future<void> restoreFromExternalApp() async {
    try {
      await _channel.invokeMethod('restoreFromExternalApp');
      await applySystemTheme();
      print('Tema restaurado desde app externa');
    } catch (e) {
      print('Error restaurando desde app externa: $e');
    }
  }

  /// Aplica animaciones de transición suaves
  Future<void> applyTransitionAnimations() async {
    try {
      await _channel.invokeMethod('applyTransitionAnimations');
      print('Animaciones de transición aplicadas');
    } catch (e) {
      print('Error aplicando animaciones: $e');
    }
  }

  /// Configura el wallpaper del sistema (si es posible)
  Future<void> setSystemWallpaper() async {
    try {
      await _channel.invokeMethod('setSystemWallpaper', {
        'type': 'gradient',
        'colors': ['0xFF0F172A', '0xFF1E293B', '0xFF334155'],
      });
      print('Wallpaper del sistema configurado');
    } catch (e) {
      print('Error configurando wallpaper: $e');
    }
  }

  /// Inicialización completa del tema
  Future<void> initializeTheme() async {
    try {
      // Cargar preferencias
      final prefs = await loadThemePreferences();
      
      // Aplicar tema del sistema
      await applySystemTheme();
      
      // Configurar animaciones
      await applyTransitionAnimations();
      
      // Configurar wallpaper si es posible
      await setSystemWallpaper();
      
      // Si está en modo kiosko, aplicar tema kiosko
      if (prefs['isKioskMode'] == true) {
        await applyKioskTheme();
      }
      
      print('Tema inicializado completamente');
    } catch (e) {
      print('Error inicializando tema: $e');
    }
  }

  /// Cleanup al cerrar la aplicación
  Future<void> cleanup() async {
    try {
      await restoreNormalTheme();
      print('Cleanup de tema completado');
    } catch (e) {
      print('Error en cleanup de tema: $e');
    }
  }
}
