import 'package:flutter/material.dart';

/// Colores del tema para el launcher orientado a personas mayores
/// Paleta navy elegante con degradados sofisticados
class AppColors {
  // Colores principales - Navy elegante
  static const Color primary = Color(0xFF1E3A8A);        // Navy profundo
  static const Color primaryLight = Color(0xFF3B82F6);   // Azul brillante
  static const Color primaryDark = Color(0xFF0F172A);    // Navy muy oscuro

  // Colores de fondo - Navy degradado
  static const Color background = Color(0xFF0F172A);     // Navy oscuro
  static const Color surface = Color(0xFF1E293B);        // Slate oscuro
  static const Color surfaceVariant = Color(0xFF334155); // Slate medio
  
  // Colores de texto - Optimizados para fondo oscuro
  static const Color textPrimary = Color(0xFFF8FAFC);    // Blanco suave
  static const Color textSecondary = Color(0xFFCBD5E1);  // Gris claro
  static const Color textLight = Color(0xFF94A3B8);      // Gris medio
  
  // Colores de acento - Cyan elegante
  static const Color accent = Color(0xFF06B6D4);         // Cyan brillante
  static const Color accentLight = Color(0xFF22D3EE);    // Cyan claro
  static const Color warning = Color(0xFFF59E0B);        // Amber
  static const Color error = Color(0xFFEF4444);          // Red moderno

  // Colores para botones - Elegantes y visibles
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = Color(0xFF475569); // Slate
  static const Color buttonDisabled = Color(0xFF64748B);  // Slate claro
  
  // Colores para el modo administrador - Más elegante
  static const Color adminPrimary = Color(0xFF7C3AED);   // Violet
  static const Color adminSecondary = Color(0xFFA855F7); // Purple
  static const Color adminBackground = Color(0xFF1E1B4B); // Indigo oscuro
  
  // Colores de estado
  static const Color success = Color(0xFF27AE60);
  static const Color info = Color(0xFF3498DB);
  static const Color connected = Color(0xFF2ECC71);
  static const Color disconnected = Color(0xFFE67E22);
  
  // Sombras y bordes
  static const Color shadow = Color(0x1A000000);
  static const Color border = Color(0xFFE1E8ED);
  static const Color borderLight = Color(0xFFF1F3F5);
  
  // Gradientes navy elegantes
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary],
    stops: [0.0, 1.0],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF0F172A), // Navy muy oscuro
      Color(0xFF1E293B), // Slate oscuro
      Color(0xFF334155), // Slate medio
    ],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient surfaceGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1E293B), // Slate oscuro
      Color(0xFF334155), // Slate medio
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient adminGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1E1B4B), // Indigo oscuro
      Color(0xFF7C3AED), // Violet
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF06B6D4), // Cyan
      Color(0xFF3B82F6), // Blue
    ],
    stops: [0.0, 1.0],
  );
}
