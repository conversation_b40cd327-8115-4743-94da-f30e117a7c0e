import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'constants/app_theme.dart';
import 'constants/app_strings.dart';
import 'screens/home_screen.dart';
import 'services/theme_persistence_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inicializar el tema navy elegante
  await ThemePersistenceService.instance.initializeTheme();

  runApp(const SeniorLauncherApp());
}

class SeniorLauncherApp extends StatelessWidget {
  const SeniorLauncherApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppStrings.appName,
      theme: AppTheme.darkTheme, // Usar tema oscuro navy
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,

      // Configuración de accesibilidad
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            // Asegurar que el texto no se escale demasiado
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.5),
            ),
          ),
          child: child!,
        );
      },
    );
  }
}


