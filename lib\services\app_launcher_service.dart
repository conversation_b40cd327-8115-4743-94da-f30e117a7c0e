import 'package:flutter/services.dart';
import 'package:installed_apps/installed_apps.dart';
import 'package:installed_apps/app_info.dart';

/// Servicio para lanzar aplicaciones y gestionar aplicaciones instaladas
class AppLauncherService {
  static const MethodChannel _channel = MethodChannel('senior_launcher/app_launcher');
  
  static AppLauncherService? _instance;
  static AppLauncherService get instance => _instance ??= AppLauncherService._();
  
  AppLauncherService._();

  /// Lanza una aplicación por su package name
  Future<bool> launchApp(String packageName) async {
    try {
      // Intentar abrir aplicaciones comunes con intents específicos
      switch (packageName) {
        case 'com.android.dialer':
        case 'phone':
          return await _launchDialer();
        case 'com.android.mms':
        case 'messages':
          return await _launchMessages();
        case 'com.android.camera2':
        case 'camera':
          return await _launchCamera();
        case 'com.android.gallery3d':
        case 'gallery':
          return await _launchGallery();
        case 'com.android.chrome':
        case 'internet':
          return await _launchBrowser();
        case 'com.android.email':
        case 'email':
          return await _launchEmail();
        case 'com.android.calendar':
        case 'calendar':
          return await _launchCalendar();
        case 'com.android.contacts':
        case 'contacts':
          return await _launchContacts();
        case 'com.android.music':
        case 'music':
          return await _launchMusic();
        case 'com.android.settings':
        case 'settings':
          return await _launchSettings();
        case 'help':
          return await _showHelp();
        default:
          return await _launchAppByPackage(packageName);
      }
    } catch (e) {
      print('Error launching app $packageName: $e');
      return false;
    }
  }

  /// Lanza el marcador telefónico
  Future<bool> _launchDialer() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.DIAL',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.dialer');
    }
  }

  /// Lanza la aplicación de mensajes
  Future<bool> _launchMessages() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.MAIN',
        'category': 'android.intent.category.APP_MESSAGING',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.mms');
    }
  }

  /// Lanza la cámara
  Future<bool> _launchCamera() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.media.action.IMAGE_CAPTURE',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.camera2');
    }
  }

  /// Lanza la galería
  Future<bool> _launchGallery() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.VIEW',
        'type': 'image/*',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.gallery3d');
    }
  }

  /// Lanza el navegador
  Future<bool> _launchBrowser() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.VIEW',
        'data': 'https://www.google.com',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.chrome');
    }
  }

  /// Lanza el email
  Future<bool> _launchEmail() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.MAIN',
        'category': 'android.intent.category.APP_EMAIL',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.email');
    }
  }

  /// Lanza el calendario
  Future<bool> _launchCalendar() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.MAIN',
        'category': 'android.intent.category.APP_CALENDAR',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.calendar');
    }
  }

  /// Lanza los contactos
  Future<bool> _launchContacts() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.VIEW',
        'data': 'content://contacts/people/',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.contacts');
    }
  }

  /// Lanza la aplicación de música
  Future<bool> _launchMusic() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.intent.action.MAIN',
        'category': 'android.intent.category.APP_MUSIC',
      });
      return result == true;
    } catch (e) {
      return await _launchAppByPackage('com.android.music');
    }
  }

  /// Lanza la configuración del sistema
  Future<bool> _launchSettings() async {
    try {
      final result = await _channel.invokeMethod('launchIntent', {
        'action': 'android.settings.SETTINGS',
      });
      return result == true;
    } catch (e) {
      return false;
    }
  }

  /// Muestra la ayuda (implementación interna)
  Future<bool> _showHelp() async {
    // Esta será manejada internamente por la app
    return true;
  }

  /// Lanza una aplicación por su package name específico
  Future<bool> _launchAppByPackage(String packageName) async {
    try {
      final result = await _channel.invokeMethod('launchApp', {
        'packageName': packageName,
      });
      return result == true;
    } catch (e) {
      print('Error launching app by package $packageName: $e');
      return false;
    }
  }

  /// Obtiene la lista de aplicaciones instaladas
  Future<List<AppInfo>> getInstalledApps() async {
    try {
      final apps = await InstalledApps.getInstalledApps(
        true, // incluir aplicaciones del sistema
        true, // incluir iconos
      );
      return apps;
    } catch (e) {
      print('Error getting installed apps: $e');
      return [];
    }
  }

  /// Verifica si una aplicación está instalada
  Future<bool> isAppInstalled(String packageName) async {
    try {
      final result = await _channel.invokeMethod('isAppInstalled', {
        'packageName': packageName,
      });
      return result == true;
    } catch (e) {
      return false;
    }
  }
}
